import { NextRequest, NextResponse } from 'next/server';

interface GenerateImageRequest {
  image_prompt: string;
  aspect_ratio: string;
  company_id?: string;
  image_gen_styles?: any;
}

interface GenerateImageResponse {
  url: string;
}

/**
 * POST handler for generating images
 * @description Generates images using external API and uploads to ImgBB
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body: GenerateImageRequest = await request.json();
    
    // Validate required fields
    if (!body.image_prompt) {
      return NextResponse.json(
        { error: 'Missing required field: image_prompt' }, 
        { status: 400 }
      );
    }

    // Generate image using external API
    const generateRes = await fetch('https://api.smartberry.ai/sb_generate_download_image_replicate_v2', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image_prompt: body.image_prompt,
        aspect_ratio: body.aspect_ratio || 'custom',
        prompt_upsampling: 'false',
        language: 'English'
      }),
    });

    if (!generateRes.ok) {
      throw new Error(`Image generation API request failed with status ${generateRes.status}`);
    }

    const imageArrayBuffer = await generateRes.arrayBuffer();
    const imageBuffer = Buffer.from(imageArrayBuffer);

    // Upload to ImgBB
    const imgbbApiKey = process.env.IMGBB_API_KEY || '********************************';
    const formData = new FormData();
    formData.append('image', imageBuffer.toString('base64'));

    const imgbbRes = await fetch(`https://api.imgbb.com/1/upload?key=${imgbbApiKey}`, {
      method: 'POST',
      body: formData,
    });

    if (!imgbbRes.ok) {
      const errorText = await imgbbRes.text();
      throw new Error(`ImgBB upload failed with status ${imgbbRes.status}: ${errorText}`);
    }

    const imgbbData = await imgbbRes.json();

    if (!imgbbData.success) {
      throw new Error(`ImgBB upload reported failure: ${JSON.stringify(imgbbData)}`);
    }

    const response: GenerateImageResponse = { url: imgbbData.data.url };
    return NextResponse.json(response);

  } catch (error) {
    console.error('Image generation error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to generate image';
    
    return NextResponse.json(
      { error: errorMessage }, 
      { status: 500 }
    );
  }
}