'use client';

import { pdfjs } from 'react-pdf';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/legacy/build/pdf.worker.min.mjs`;

interface PDFViewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  fileUrl: string;
  fileName: string;
  content?: string;
  fileType?: string;
}

export function PDFViewerModal({
  isOpen,
  onClose,
  fileUrl,
  fileName,
  content,
  fileType,
}: PDFViewerModalProps) {
  // const [numPages, setNumPages] = useState<number>();
  const isWebsiteContent = fileType === 'Website';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[80vh] overflow-auto w-[95vw] max-w-[95vw] sm:w-full sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle>{fileName}</DialogTitle>
        </DialogHeader>
        <div className="p-4">
          {isWebsiteContent ? (
            <div className="prose max-w-none rounded-lg bg-white p-6">
                  <p  className="mb-4">
                    {content}
                  </p>
              {/* {content?.split('\n').map((paragraph, index) =>
               
              )} */}
            </div>
          ) : (
            <iframe
              src={fileUrl}
              className="h-[70vh] w-full"
              title={fileName}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
