import React, { useState } from 'react';
import { Info, Loader2 } from "lucide-react";
import { Label } from "@kit/ui/label";
import { Tooltip, TooltipContent, TooltipTrigger } from "@kit/ui/tooltip";
import { Textarea } from "@kit/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@kit/ui/select";
import { useImageContent } from '../../../context';
import { useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { toast } from 'sonner';
import { useParams } from 'next/navigation';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

// Types for visual description options
interface VisualDescriptionOption {
  level: string;
  prompt: string;
}

interface VisualDescriptionResponse {
  data: VisualDescriptionOption[];
}

export const VisualDescriptionInput: React.FC = () => {
  // Get input/setInput from context
  const { input, setInput } = useImageContent();
  const zero = useZero();
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const [generatedOptions, setGeneratedOptions] = useState<VisualDescriptionOption[]>([]);
  const [selectedOption, setSelectedOption] = useState<string>('');
  const params = useParams();
  const workspace = useTeamAccountWorkspace();
  const [companyBrand] = useZeroQuery(
    zero.query.company_brand
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '10m'
    }
  );
  
  const [companyContent] = useZeroQuery(
    zero.query.company_content
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '10m'
    }
  );
  const filteredCompanyContent = companyContent.filter((content: any) => content.id == params.id)[0] || {} as any;

  const handleGetVisualDescription = async () => {
    try {
      setIsGeneratingDescription(true);
      
      const response = await fetch('/api/ai/generate-visual-description', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brand_brief: companyBrand || "NOT_PROVIDED",
          content: filteredCompanyContent.task_description || "NOT_PROVIDED",
          initial_visual_desc: input || "NOT_PROVIDED",
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const visualDescription: VisualDescriptionResponse = await response.json();
      
      
      
      // Store the generated options
      setGeneratedOptions(visualDescription.data);
      
      // Set the first option as default
      if (visualDescription.data && visualDescription.data.length > 0) {
        setSelectedOption('0');
        setInput(visualDescription.data[0]?.prompt || '');
      }
      
      setIsGeneratingDescription(false);
    } catch (error) {
      setIsGeneratingDescription(false);
      toast.error('Error generating visual description');
      console.error('Error generating visual description:', error);
    }
  }

  const handleOptionChange = (value: string) => {
    setSelectedOption(value);
    const selectedIndex = parseInt(value);
    if (generatedOptions[selectedIndex]) {
      setInput(generatedOptions[selectedIndex].prompt);
    }
  }

  return (
    <>
      <div className="flex mt-10 items-center gap-2">
        <Label htmlFor="visual-description">Visual Description</Label>
        <Tooltip>
          <TooltipTrigger asChild>
            <Info className="h-4 w-4 text-muted-foreground cursor-help" />
          </TooltipTrigger>
          <TooltipContent>
            <p className="max-w-xs">Describe the image you want to generate in detail. The more specific your description, the better the generated image will match your vision.</p>
          </TooltipContent>
        </Tooltip>
      </div>
      <p 
        onClick={() => !isGeneratingDescription && handleGetVisualDescription()} 
        className={`underline text-blue-400 text-xs ${isGeneratingDescription ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'} flex items-center gap-2`}
      >
        {isGeneratingDescription && <Loader2 className="h-3 w-3 animate-spin" />}
        {input ? "Improve Visual Description for me." : "Generate Visual Description for me."}
      </p>

      {/* Show options selector if we have generated options */}
      {generatedOptions.length > 0 && (
        <div className="mt-2">
          <Label htmlFor="description-options">Choose Brand Integration Level:</Label>
          <Select value={selectedOption} onValueChange={handleOptionChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a brand integration level" />
            </SelectTrigger>
            <SelectContent>
              {generatedOptions.map((option, index) => (
                <SelectItem key={index} value={index.toString()}>
                  {option.level}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <Textarea
        id="visual-description"
        placeholder="Describe the image you want to generate..."
        value={input}
        onChange={(e) => setInput(e.target.value)}
        className="min-h-[100px]"
      />
    </>
  );
};