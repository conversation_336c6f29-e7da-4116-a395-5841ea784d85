import React, { useEffect, useCallback } from 'react';
import { X, ChevronLeft, ChevronRight, Save, Plus } from "lucide-react";
import { But<PERSON> } from "@kit/ui/button";
import { cn } from "@kit/ui/utils";
import { useBaseContent, useImageContent } from '../../context';
import { useZero } from '~/hooks/use-zero';
import { useParams } from 'next/navigation';
import { useQuery as useZeroQuery } from "@rocicorp/zero/react";
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import Image from 'next/image';

interface ImageGalleryProps {
  imageUrls: string[];
}

export const ImageGallery: React.FC<ImageGalleryProps> = ({
  imageUrls,
}) => {
  // Get selectedImageUrl from context
  const { selectedImageUrl, setSelectedImageUrl, enlargedImageUrl, setEnlargedImageUrl } = useImageContent();
  const params = useParams();
  const contentId = params.id;
  const zero = useZero();  
  const workspace = useTeamAccountWorkspace();
  const [companyContent] = useZeroQuery(
    zero.query.company_content
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '10m'
    }
  );
  
  const selectedCompanyContent = companyContent.filter((content: any) => content.id === contentId)[0];  

  const savedImages = selectedCompanyContent?.image_urls || [];
  // Navigate to previous or next image
  const navigateImage = useCallback((direction: 'prev' | 'next') => {
    if (!enlargedImageUrl) return;
    
    const currentIndex = imageUrls.findIndex(url => url === enlargedImageUrl);
    if (currentIndex === -1) return;
    
    let newIndex;
    if (direction === 'prev') {
      newIndex = currentIndex === 0 ? imageUrls.length - 1 : currentIndex - 1;
    } else {
      newIndex = currentIndex === imageUrls.length - 1 ? 0 : currentIndex + 1;
    }
    // @ts-expect-error TO do - fix this typing error 
    setEnlargedImageUrl(imageUrls[newIndex]);
  }, [enlargedImageUrl, imageUrls, setEnlargedImageUrl]);

  // Handle keyboard events for navigation and closing
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!enlargedImageUrl) return;
      
      if (e.key === 'Escape') {
        setEnlargedImageUrl(null);
      } else if (e.key === 'ArrowLeft') {
        navigateImage('prev');
      } else if (e.key === 'ArrowRight') {
        navigateImage('next');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [enlargedImageUrl, navigateImage, setEnlargedImageUrl]);
  
  // Prevent scrolling when modal is open
  useEffect(() => {
    if (enlargedImageUrl) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [enlargedImageUrl]);
  
  if (imageUrls.length === 0) {
    return null;
  }
  
  // Utility function to detect if URL is a video
  const isVideoUrl = (url: string): boolean => {
    return url.includes('.mp4') || url.includes('.mov') || url.includes('.avi') || url.includes('.webm');
  };

  const save = (url: string) => {
    
    // When adding an image, remove any existing videos (social platforms only support one or the other)
    const currentImages = (selectedCompanyContent?.image_urls as string[]) || [];
    const existingImages = currentImages.filter(existingUrl => !isVideoUrl(existingUrl));
    
    zero.mutate.company_content.update({
      id: selectedCompanyContent?.id || "",
              values: {
          image_urls: [...existingImages, url]
        }
    });
  }

  return (
    <div className="space-y-4 mt-8">
      <div className="grid grid-cols-2 gap-4">
        {imageUrls.map((url, index) => (
          <div key={index} className="flex flex-col gap-4">
          <div
            key={index}
            className={cn(
              "relative cursor-pointer rounded-lg overflow-hidden border-2",
              selectedImageUrl === url ? "border-primary" : "border-transparent"
            )}
            onClick={() => {
              setSelectedImageUrl(url);
              setEnlargedImageUrl(url);
            }}
          >
            <Image
              src={url}
              alt={`Generated image ${index + 1}`}
              className="w-full h-auto object-cover"
              width={400}
              height={400}
              unoptimized
            />
         
          </div>
             <Button
             variant="outline"
             size="icon"
             className="flex items-center justify-center w-full"
             onClick={() => save(url)}
           >
             <Plus className="h-6 w-6" /> Add 
           </Button>
           </div>
        ))}
      </div>
   
      {/* Custom fullscreen modal */}
      {enlargedImageUrl && (
        <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-2">
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Close button */}
            <Button
              variant="outline"
              size="icon"
              className="absolute right-2 top-2 z-10 rounded-full bg-background/80 hover:bg-background"
              onClick={() => setEnlargedImageUrl(null)}
            >
              <X className="h-6 w-6" />
            </Button>
            
            {/* Left navigation arrow */}
            <Button
              variant="outline"
              size="icon"
              className="absolute left-2 top-1/2 -translate-y-1/2 z-10 rounded-full bg-background/80 hover:bg-background"
              onClick={() => navigateImage('prev')}
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>
            
            {/* Right navigation arrow */}
            <Button
              variant="outline"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 z-10 rounded-full bg-background/80 hover:bg-background"
              onClick={() => navigateImage('next')}
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
            
            {/* Image container */}
            <div className="flex items-center justify-center w-full h-full">
              <Image
                src={enlargedImageUrl}
                alt="Enlarged view"
                height={1900}
                width={1900}
                className="max-w-[90vw] max-h-[90vh] object-contain mx-auto"
                unoptimized
                />
            </div>
            
            {/* Image counter */}
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-background/80 px-3 py-1 rounded-full text-sm">
              {imageUrls.findIndex(url => url === enlargedImageUrl) + 1} / {imageUrls.length}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};