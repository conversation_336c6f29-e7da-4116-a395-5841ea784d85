import React from 'react';
import { <PERSON><PERSON> } from "@kit/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@kit/ui/tabs";
import { Loader2 } from "lucide-react";
import { VisualDescriptionInput } from './visual-description-input';
import { AdvancedImageOptions } from '../advanced-image-options';
import { ImageGallery } from '../image-gallery';
import { useImageContent } from '../../../context';

interface AIImageGeneratorProps {
  onSubmit: () => void;
  isGenerating: boolean;
}

export const AIImageGenerator: React.FC<AIImageGeneratorProps> = ({
  onSubmit,
  isGenerating
}) => {
  // Get state from context
  const { input, imageUrls } = useImageContent();

  return (
    <div className="space-y-4">
      {/* Visual Description Input - Shared between basic and advanced */}
      <VisualDescriptionInput />
      
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          {/* Basic mode - just the visual description input above */}
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <AdvancedImageOptions />
        </TabsContent>
      </Tabs>

      <Button 
        className="w-full" 
        onClick={onSubmit}
        disabled={isGenerating || !input.trim()}
      >
        {isGenerating ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Generating...
          </>
        ) : (
          'Generate Images'
        )}
      </Button>

      {imageUrls.length > 0 && (
        <ImageGallery imageUrls={imageUrls} />
      )}
    </div>
  );
};