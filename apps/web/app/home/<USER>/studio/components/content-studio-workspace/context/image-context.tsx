import React, { createContext, useContext, useState, ReactNode } from 'react';
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";
import { useBrandData } from "~/hooks/use-brand-data";
import { extractBrandBrief } from "~/utils/brief.util";
import { 
  ImageContentContextState, 
  VisualDescriptionGroup, 
  ImageGenerationOptions,
  ImageOptionType
} from './types';

// Create the image content context
const ImageContentContext = createContext<ImageContentContextState | undefined>(undefined);

// Provider component for image content context
export function ImageContentProvider({ children }: { children: ReactNode }) {
  const [selectedImageOption, setSelectedImageOption] = useState<ImageOptionType | null>(null);
  const [input, setInput] = useState('');
  const [imageOptions, setImageOptions] = useState<ImageGenerationOptions>({});
  
  // Wrap setImageOptions to add logging
  const setImageOptionsWithLogging = (newOptions: ImageGenerationOptions | ((prev: ImageGenerationOptions) => ImageGenerationOptions)) => {
    setImageOptions(newOptions);
  };
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [selectedImageId, setSelectedImageId] = useState<string | null>(null);
  const [enlargedImageUrl, setEnlargedImageUrl] = useState<string | null>(null);
  const [visualDescriptionGroup, setVisualDescriptionGroup] = useState<VisualDescriptionGroup>({
    Balanced: '',
    Subtle: '',
    Learned: '',
    Prominent: '',
    Full: ''
  });
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [selectedEditorImage, setSelectedEditorImage] = useState<string | null>(null);
  
  // External hooks for API calls
  const workspace = useTeamAccountWorkspace();
  const brand = useBrandData(workspace.account.id);
  
  // API methods for image generation
  const generateVisualDescription = async (
    input: string, 
    imageOptions: ImageGenerationOptions,
    companyContentId: string
  ) => {
    
   
    // Format image generation styles
    const image_gen_styles = formatImageStyles(imageOptions);
    
    try {
      const response = await fetch('/api/ai/generate-idea-visual-description', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          brand_name: workspace.account.name,
          brand_brief: brand.data?.brand ? extractBrandBrief(brand.data.brand) : "No Brand Brief Provided",
          product_info: (brand.data?.brand as any)?.product_list || "No Product Info Provided",
          generated_content: input || "No Generated Content Provided",
          initial_visual_desc: input || "No Initial Visual Description Provided",
          language: "English",
          image_gen_styles
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      
      setInput(data.visual_description.Balanced);
      setVisualDescriptionGroup(data.visual_description);
      return data;
    } catch (error) {
      console.error('Error generating visual description:', error);
      throw error;
    }
  };

  // Generate images based on visual descriptions
  const generateImages = async (
    visualDescriptionGroup: VisualDescriptionGroup, 
    imageOptions: ImageGenerationOptions
  ) => {
    const imagePrompts = Object.values(visualDescriptionGroup).filter(prompt => prompt.trim() !== "");
    
    // Format image generation styles
    const image_gen_styles = formatImageStyles(imageOptions);

    const promises = imagePrompts.map(async (prompt) => {
      try {
        const imageRoute = process.env.NODE_ENV === 'development' ? '/api/latest/generate-image' : '/api/ai/generate-image';
        
        const requestBody = {
          image_prompt: input + " " + JSON.stringify(image_gen_styles),
          aspect_ratio: "custom",
          company_id: workspace.account.id,
          image_gen_styles
        };
        
        const response = await fetch(imageRoute, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          throw new Error(`Image generation failed with status ${response.status}`);
        }
        
        const data = await response.json();
        
        
        setImageUrls(prev => [...prev, data.url]);
        return data.url;
      } catch (error) {
        console.error('Error generating image:', error);
        throw error;
      }
    });
    
    return Promise.all(promises);
  };
  
  // Helper function to format image styles
  const formatImageStyles = (imageOptions: ImageGenerationOptions) => {
    const image_gen_styles: any = {
      artistic_influences_references: imageOptions?.artistic_influences_references || null,
      image_camera_angle_perspective: imageOptions?.image_camera_angle_perspective || undefined,
      image_color_balance: imageOptions?.image_color_balance || undefined,
      image_composition_framing: imageOptions?.image_composition_framing || undefined,
      image_details: imageOptions?.image_details || undefined,
      image_environment_setting: imageOptions?.image_environment_setting || undefined,
      image_lighting: imageOptions?.image_lighting || undefined,
      image_material_surface_texture: imageOptions?.image_material_surface_texture || undefined,
      image_modes: imageOptions?.image_modes || undefined,
      image_mood_atmosphere: imageOptions?.image_mood_atmosphere || undefined,
      image_special_effects_post_processing: imageOptions?.image_special_effects_post_processing || undefined,
      image_styles: imageOptions?.image_styles || undefined,
      main_actions: [],
      main_colors: [],
      main_elements: []
    };

    // Remove any undefined properties
    Object.keys(image_gen_styles).forEach(key => {
      if (image_gen_styles[key] === undefined) {
        delete image_gen_styles[key];
      }
    });
    
    return image_gen_styles;
  };
  
  const value = {
    selectedImageOption,
    setSelectedImageOption,
    input,
    setInput,
    imageOptions,
    setImageOptions: setImageOptionsWithLogging,
    selectedImageUrl,
    setSelectedImageUrl,
    selectedImageId,
    setSelectedImageId,
    enlargedImageUrl,
    setEnlargedImageUrl,
    visualDescriptionGroup,
    setVisualDescriptionGroup,
    imageUrls,
    setImageUrls,
    selectedEditorImage,
    setSelectedEditorImage,
    generateVisualDescription,
    generateImages
  };

  return (
    <ImageContentContext.Provider value={value}>
      {children}
    </ImageContentContext.Provider>
  );
}

// Custom hook for accessing the image content context
export function useImageContent() {
  const context = useContext(ImageContentContext);
  if (context === undefined) {
    throw new Error('useImageContent must be used within an ImageContentProvider');
  }
  return context;
}